import { AuthProvider, useAuth } from "./providers";
import { AuthGuard } from "./components";
import {
  makeStore,
  useAppDispatch,
  useAppSelector,
  useAppStore,
  type RootState,
  type AppStore,
  type AppDispatch
} from "./store";
import { getFirstZodError, getLCRequestCommonHeaders, getDevice } from "./utils";
import { lcBackendRequest } from "./lib";
import { lcAuthRequest, type ILoginFormPayload } from "./store/features/auth/authAPI";
import { lcFbSignInWithCustomToken } from "./firebase";

// Redux exports
import {
  setUser,
  setIsAuthenticated,
  setIsAuthLoading,
  setCurrentPage,
  setIsLoading,
  setLoginError,
  setLoginData,
  setAuthData,
  setAuth,
  setAvailableOtpChannels,
  setSelectedOtpChannel,
  setIsResendingOtp,
  clearAuth,
  convertUserToSerializable,
  type IAuthData,
  type ILoginData,
  type ISerializableUser
} from "./store/features/auth/authSlice";

import {
  useSubmitLoginMutation,
  useSubmitOtpMutation,
  useRefreshTokenMutation,
  useLogoutMutation,
  type LoginResponse,
  type OtpResponse
} from "./store/features/auth/authApiSlice";

import {
  submitLoginForm,
  submitOtp,
  logoutUser,
  checkAuthStatus,
  syncAuthStorage,
  resendOtp
} from "./store/features/auth/authThunks";

// User slice exports
import {
  setCurrentUser,
  setPasswordUpdatedInOnboarding,
  setInternalUser,
  setLoading as setUserLoading,
  setUpdating,
  setError as setUserError,
  updatePreferences,
  updateNotificationPreferences,
  updateSession,
  setIdle,
  updateLastActivity,
  setSubscriptionStatus,
  clearUserState,
  clearError as clearUserError,
  updateCurrentUser,
  setUserTimezone,
  addUserLocation,
  removeUserLocation,
  updateUserPermissions,
  type UserState
} from "./store/features/user/userSlice";

import {
  getCurrentUser,
  createUser,
  updateUser,
  deleteUser,
  handlePasswordChanged,
  addLocationToUser,
  removeLocationFromUser,
  checkInternalUser,
  syncUserData,
  initializeUserSession
} from "./store/features/user/userThunks";

// User selectors
import {
  selectCurrentUser,
  selectUserComputedProperties,
  selectUserDisplayName,
  selectUserFullName,
  selectUserProfileColor,
  selectUserPermissions,
  selectUserPermissionChecks,
  selectIsAdmin,
  selectCanAccessAll,
  selectIsAssignedTo,
  selectCanViewOpportunities,
  selectCanCreateReviewRequest,
  selectCanAccessCampaigns,
  selectCanAccessWorkflows,
  selectCanAccessContacts,
  selectCanAccessSettings,
  selectUserLocations,
  selectAccessibleLocationIds,
  selectUserRole,
  selectUserType,
  selectUserCompanyId,
  selectIsUserActive,
  selectUserInfo
} from "./store/features/user/userSelectors";

// User types and utilities
import type {
  IUser as IUserComplete,
  ICreateUserPayload,
  IUpdateUserPayload,
  IUserQueryParams,
  IUserListResponse,
  IUserPermissions,
  IUserPermissionChecks,
  IUserComputedProperties,
  UserRole,
  UserType,
  UserSource,
  UserSourceChannel
} from "./types/user";

import {
  UserComputedUtils,
  UserPermissionUtils,
  UserLocationUtils,
  UserValidationUtils
} from "./utils/userUtils";

// Base types
import type {
  IBaseEntity,
  IAuditInfo,
  IAuditableEntity,
  IEntity,
  IQueryOptions,
  IPaginatedResponse,
  IMutationResponse,
  IApiResponse
} from "./types/base";

// Model utilities
import {
  DateUtils,
  ValidationUtils,
  ChangeTracker,
  EntityUtils
} from "./utils/modelUtils";

// Import all API hooks and types
import {
  // Notifications API
  useGetNotificationsQuery,
  useGetNotificationReportingQuery,
  useMarkNotificationAsReadMutation,
  useMarkAllNotificationsAsReadMutation,
  type INotification,
  type IGetNotificationsParams,
  type IGetNotificationsResponse,
  type INotificationReportingParams,
  type INotificationReportingResponse,

  // Custom Fields API
  useGetCustomFieldFoldersQuery,
  useGetCustomFieldsQuery,
  useCreateCustomFieldMutation,
  useUpdateCustomFieldMutation,
  useDeleteCustomFieldMutation,
  type ICustomField,
  type ICustomFieldFolder,
  type ICustomFieldsSearchParams,
  type ICustomFieldFoldersResponse,
  type ICustomFieldsResponse,
  type ICreateCustomFieldRequest,
  type IUpdateCustomFieldRequest,

  // Users API - Enhanced
  useGetAllUsersQuery,
  useFindUserByEmailQuery,
  useGetUsersByLocationQuery,
  useGetAgencyUsersQuery,
  useGetLocationCompanyUsersQuery,
  useGetUsersByPhoneQuery,
  useGetUserByIdQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useToggleUserStatusMutation,
  useHandlePasswordChangedMutation,
  useSyncGlobalListsForAllLocationsMutation,
  useSyncGlobalListsForLocationMutation,
  useRemoveSmartListsForLocationMutation,

  // Locations API
  useSearchLocationsQuery,
  useGetLocationByIdQuery,
  useCreateLocationMutation,
  useUpdateLocationMutation,
  useDeleteLocationMutation,
  useRefreshLocationTokenMutation,
  useToggleLocationStatusMutation,
  type ILocation,
  type ISearchLocationsParams,
  type ISearchLocationsResponse,
  type IGetLocationByIdParams,
  type IGetLocationByIdResponse,
  type ICreateLocationRequest,
  type IUpdateLocationRequest,
  type ILocationRefreshTokenRequest,
  type ILocationRefreshTokenResponse,
} from "./store/features/api";

export {
  // Auth Provider & Components
  AuthProvider,
  useAuth,
  AuthGuard,

  // Redux Store
  makeStore,
  useAppDispatch,
  useAppSelector,
  useAppStore,

  // Redux Actions
  setUser,
  setIsAuthenticated,
  setIsAuthLoading,
  setCurrentPage,
  setIsLoading,
  setLoginError,
  setLoginData,
  setAuthData,
  setAuth,
  setAvailableOtpChannels,
  setSelectedOtpChannel,
  setIsResendingOtp,
  clearAuth,

  // Redux Thunks - Auth
  submitLoginForm,
  submitOtp,
  logoutUser,
  checkAuthStatus,
  syncAuthStorage,
  resendOtp,

  // Redux Actions - User
  setCurrentUser,
  setPasswordUpdatedInOnboarding,
  setInternalUser,
  setUserLoading,
  setUpdating,
  setUserError,
  updatePreferences,
  updateNotificationPreferences,
  updateSession,
  setIdle,
  updateLastActivity,
  setSubscriptionStatus,
  clearUserState,
  clearUserError,
  updateCurrentUser,
  setUserTimezone,
  addUserLocation,
  removeUserLocation,
  updateUserPermissions,

  // Redux Thunks - User
  getCurrentUser,
  createUser,
  updateUser,
  deleteUser,
  handlePasswordChanged,
  addLocationToUser,
  removeLocationFromUser,
  checkInternalUser,
  syncUserData,
  initializeUserSession,

  // User Selectors
  selectCurrentUser,
  selectUserComputedProperties,
  selectUserDisplayName,
  selectUserFullName,
  selectUserProfileColor,
  selectUserPermissions,
  selectUserPermissionChecks,
  selectIsAdmin,
  selectCanAccessAll,
  selectIsAssignedTo,
  selectCanViewOpportunities,
  selectCanCreateReviewRequest,
  selectCanAccessCampaigns,
  selectCanAccessWorkflows,
  selectCanAccessContacts,
  selectCanAccessSettings,
  selectUserLocations,
  selectAccessibleLocationIds,
  selectUserRole,
  selectUserType,
  selectUserCompanyId,
  selectIsUserActive,
  selectUserInfo,

  // User Utilities
  UserComputedUtils,
  UserPermissionUtils,
  UserLocationUtils,
  UserValidationUtils,

  // Model Utilities
  DateUtils,
  ValidationUtils,
  ChangeTracker,
  EntityUtils,

  // RTK Query Hooks - Auth
  useSubmitLoginMutation,
  useSubmitOtpMutation,
  useRefreshTokenMutation,
  useLogoutMutation,

  // RTK Query Hooks - Notifications
  useGetNotificationsQuery,
  useGetNotificationReportingQuery,
  useMarkNotificationAsReadMutation,
  useMarkAllNotificationsAsReadMutation,

  // RTK Query Hooks - Custom Fields
  useGetCustomFieldFoldersQuery,
  useGetCustomFieldsQuery,
  useCreateCustomFieldMutation,
  useUpdateCustomFieldMutation,
  useDeleteCustomFieldMutation,

  // RTK Query Hooks - Users (Enhanced)
  useGetAllUsersQuery,
  useFindUserByEmailQuery,
  useGetUsersByLocationQuery,
  useGetAgencyUsersQuery,
  useGetLocationCompanyUsersQuery,
  useGetUsersByPhoneQuery,
  useGetUserByIdQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useToggleUserStatusMutation,
  useHandlePasswordChangedMutation,
  useSyncGlobalListsForAllLocationsMutation,
  useSyncGlobalListsForLocationMutation,
  useRemoveSmartListsForLocationMutation,

  // RTK Query Hooks - Locations
  useSearchLocationsQuery,
  useGetLocationByIdQuery,
  useCreateLocationMutation,
  useUpdateLocationMutation,
  useDeleteLocationMutation,
  useRefreshLocationTokenMutation,
  useToggleLocationStatusMutation,

  // Utilities
  getFirstZodError,
  getLCRequestCommonHeaders,
  getDevice,
  lcBackendRequest,
  lcAuthRequest,
  lcFbSignInWithCustomToken,
  convertUserToSerializable,

  // Types - Auth
  type ILoginFormPayload,
  type IAuthData,
  type ILoginData,
  type ISerializableUser,
  type LoginResponse,
  type OtpResponse,

  // Types - Notifications
  type INotification,
  type IGetNotificationsParams,
  type IGetNotificationsResponse,
  type INotificationReportingParams,
  type INotificationReportingResponse,

  // Types - Custom Fields
  type ICustomField,
  type ICustomFieldFolder,
  type ICustomFieldsSearchParams,
  type ICustomFieldFoldersResponse,
  type ICustomFieldsResponse,
  type ICreateCustomFieldRequest,
  type IUpdateCustomFieldRequest,

  // Types - Users (Enhanced)
  type IUserComplete,
  type ICreateUserPayload,
  type IUpdateUserPayload,
  type IUserQueryParams,
  type IUserListResponse,
  type IUserPermissions,
  type IUserPermissionChecks,
  type IUserComputedProperties,
  type UserState,
  type UserRole,
  type UserType,
  type UserSource,
  type UserSourceChannel,

  // Types - Base Models
  type IBaseEntity,
  type IAuditInfo,
  type IAuditableEntity,
  type IEntity,
  type IQueryOptions,
  type IPaginatedResponse,
  type IMutationResponse,
  type IApiResponse,

  // Types - Locations
  type ILocation,
  type ISearchLocationsParams,
  type ISearchLocationsResponse,
  type IGetLocationByIdParams,
  type IGetLocationByIdResponse,
  type ICreateLocationRequest,
  type IUpdateLocationRequest,
  type ILocationRefreshTokenRequest,
  type ILocationRefreshTokenResponse,

  // Types - Store
  type RootState,
  type AppStore,
  type AppDispatch
};