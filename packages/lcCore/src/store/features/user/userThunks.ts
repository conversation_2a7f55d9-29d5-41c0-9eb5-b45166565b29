/**
 * User Thunks
 * 
 * Async thunks for complex user operations and business logic
 * Based on SPM Vuex user store actions and User model methods
 */

import { createAsyncThunk } from '@reduxjs/toolkit';
import type { RootState } from '../../index';
import type { IUser, ICreateUserPayload, IUpdateUserPayload } from '../../../types/user';
import { 
  setCurrentUser, 
  setLoading, 
  setUpdating, 
  setError, 
  clearUserState,
  setInternalUser,
  updateCurrentUser,
  addUserLocation,
  removeUserLocation
} from './userSlice';
import { UserValidationUtils, EntityUtils } from '../../../utils';

/**
 * Get current user data
 * Based on SPM user store 'get' action
 */
export const getCurrentUser = createAsyncThunk<
  IUser,
  void,
  { state: RootState }
>(
  'user/getCurrentUser',
  async (_, { getState, dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      
      const state = getState();
      const authData = state.auth.authData;
      
      if (!authData?.userId) {
        throw new Error('No authenticated user found');
      }

      // In a real implementation, this would call the API
      // For now, we'll use the auth user data
      const authUser = state.auth.user;
      if (!authUser) {
        throw new Error('User data not available');
      }

      // Convert auth user to full user object
      const user: IUser = authUser as unknown as IUser;
      
      dispatch(setCurrentUser(user));
      return user;
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to get user data';
      dispatch(setError(message));
      return rejectWithValue(message);
    } finally {
      dispatch(setLoading(false));
    }
  }
);

/**
 * Create a new user
 * Based on SPM User model creation patterns
 */
export const createUser = createAsyncThunk<
  IUser,
  ICreateUserPayload,
  { state: RootState }
>(
  'user/createUser',
  async (userData, { getState, dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      
      // Validate user data
      const validation = UserValidationUtils.validateCreateUser(userData);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      const state = getState();
      const currentUserId = state.auth.authData?.userId;
      
      // Prepare user data for creation
      const preparedData = EntityUtils.prepareForCreate(userData, currentUserId);
      
      // In a real implementation, this would call the API
      // For now, we'll simulate the creation
      const newUser: IUser = {
        id: `user_${Date.now()}`,
        ...preparedData,
        firstNameLowerCase: userData.firstName.toLowerCase(),
        lastNameLowerCase: userData.lastName.toLowerCase(),
        permissions: userData.permissions || {},
        locations: userData.locations || {},
        isActive: userData.isActive !== false,
        role: userData.role,
        type: userData.type,
        companyId: userData.companyId,
      };

      return newUser;
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to create user';
      dispatch(setError(message));
      return rejectWithValue(message);
    } finally {
      dispatch(setLoading(false));
    }
  }
);

/**
 * Update user data
 * Based on SPM User model save method
 */
export const updateUser = createAsyncThunk<
  IUser,
  { userId: string } & IUpdateUserPayload,
  { state: RootState }
>(
  'user/updateUser',
  async ({ userId, ...updateData }, { getState, dispatch, rejectWithValue }) => {
    try {
      dispatch(setUpdating(true));
      
      // Validate update data
      const validation = UserValidationUtils.validateUpdateUser(updateData);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      const state = getState();
      const currentUserId = state.auth.authData?.userId;
      
      // Prepare update data
      const preparedData = EntityUtils.prepareForUpdate(updateData, currentUserId);
      
      // Add lowercase fields if name fields are being updated
      const extendedData = preparedData as any;
      if (updateData.firstName) {
        extendedData.firstNameLowerCase = updateData.firstName.toLowerCase();
      }
      if (updateData.lastName) {
        extendedData.lastNameLowerCase = updateData.lastName.toLowerCase();
      }

      // Optimistic update
      dispatch(updateCurrentUser(preparedData));
      
      // In a real implementation, this would call the API
      // For now, we'll simulate the update
      const currentUser = state.user.currentUser;
      if (!currentUser) {
        throw new Error('No current user to update');
      }

      const updatedUser: IUser = {
        ...currentUser,
        ...preparedData,
      };

      dispatch(setCurrentUser(updatedUser));
      return updatedUser;
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to update user';
      dispatch(setError(message));
      return rejectWithValue(message);
    } finally {
      dispatch(setUpdating(false));
    }
  }
);

/**
 * Delete user (soft delete)
 * Based on SPM User model delete method
 */
export const deleteUser = createAsyncThunk<
  void,
  string,
  { state: RootState }
>(
  'user/deleteUser',
  async (userId, { getState, dispatch, rejectWithValue }) => {
    try {
      dispatch(setUpdating(true));
      
      const state = getState();
      const currentUserId = state.auth.authData?.userId;
      
      // Prepare delete data
      const deleteData = EntityUtils.prepareForDelete(currentUserId);
      
      // In a real implementation, this would call the API
      // For now, we'll simulate the deletion
      
      // If deleting current user, clear user state
      if (userId === state.user.currentUser?.id) {
        dispatch(clearUserState());
      }
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to delete user';
      dispatch(setError(message));
      return rejectWithValue(message);
    } finally {
      dispatch(setUpdating(false));
    }
  }
);

/**
 * Handle password change
 * Based on SPM User.passwordChanged method
 */
export const handlePasswordChanged = createAsyncThunk<
  void,
  string,
  { state: RootState }
>(
  'user/handlePasswordChanged',
  async (userId, { dispatch, rejectWithValue }) => {
    try {
      // In a real implementation, this would call the API
      // Update localStorage to prevent logout
      const loginDate = new Date();
      localStorage.setItem('loginDate', loginDate.toISOString());
      
      // Update user data
      dispatch(updateCurrentUser({
        lastPasswordChange: new Date().toISOString(),
        isPasswordPending: false,
      }));
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to handle password change';
      dispatch(setError(message));
      return rejectWithValue(message);
    }
  }
);

/**
 * Add location to user
 * Based on SPM User.addLocation method
 */
export const addLocationToUser = createAsyncThunk<
  void,
  { userId: string; locationId: string; notificationType: string },
  { state: RootState }
>(
  'user/addLocationToUser',
  async ({ userId, locationId, notificationType }, { dispatch, rejectWithValue }) => {
    try {
      // Optimistic update
      dispatch(addUserLocation({ locationId, notificationType }));
      
      // In a real implementation, this would call the API
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to add location to user';
      dispatch(setError(message));
      return rejectWithValue(message);
    }
  }
);

/**
 * Remove location from user
 * Based on SPM User.removeLocation method
 */
export const removeLocationFromUser = createAsyncThunk<
  void,
  { userId: string; locationId: string },
  { state: RootState }
>(
  'user/removeLocationFromUser',
  async ({ userId, locationId }, { dispatch, rejectWithValue }) => {
    try {
      // Optimistic update
      dispatch(removeUserLocation(locationId));
      
      // In a real implementation, this would call the API
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to remove location from user';
      dispatch(setError(message));
      return rejectWithValue(message);
    }
  }
);

/**
 * Check if user is internal (support team)
 * Based on SPM user store internal user detection
 */
export const checkInternalUser = createAsyncThunk<
  boolean,
  void,
  { state: RootState }
>(
  'user/checkInternalUser',
  async (_, { dispatch }) => {
    try {
      // Check if user came from support portal
      const appReferer = localStorage.getItem('app_referer');
      const isInternal = appReferer === 'https://support.leadconnectorhq.com/';
      
      dispatch(setInternalUser(isInternal));
      return isInternal;
      
    } catch (error) {
      // Fail silently for internal user check
      return false;
    }
  }
);

/**
 * Sync user data with real-time updates
 * Based on SPM user store real-time listener patterns
 */
export const syncUserData = createAsyncThunk<
  void,
  IUser,
  { state: RootState }
>(
  'user/syncUserData',
  async (userData, { getState, dispatch }) => {
    try {
      const state = getState();
      const currentUser = state.user.currentUser;
      
      // Check for significant changes that might require logout
      if (currentUser && (
        userData.deleted === true ||
        userData.isActive === false ||
        userData.role !== currentUser.role
      )) {
        // In a real implementation, this would trigger logout
        console.warn('User status changed, logout may be required');
      }
      
      // Update user data
      dispatch(setCurrentUser(userData));
      
    } catch (error) {
      console.error('Failed to sync user data:', error);
    }
  }
);

/**
 * Initialize user session
 * Based on SPM user store initialization patterns
 */
export const initializeUserSession = createAsyncThunk<
  void,
  void,
  { state: RootState }
>(
  'user/initializeUserSession',
  async (_, { dispatch }) => {
    try {
      // Check for internal user
      await dispatch(checkInternalUser());
      
      // Get current user data
      await dispatch(getCurrentUser());
      
    } catch (error) {
      console.error('Failed to initialize user session:', error);
    }
  }
);
