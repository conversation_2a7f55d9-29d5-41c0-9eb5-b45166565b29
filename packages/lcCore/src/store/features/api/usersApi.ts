/**
 * Enhanced Users API
 *
 * Comprehensive RTK Query API slice for user management
 * Based on SPM User model static methods and functionality
 */

import { createApi } from "@reduxjs/toolkit/query/react";
import { lcAuthRequest } from "@utils";
import type {
  IUser,
  ICreateUserPayload,
  IUpdateUserPayload,
  UserRole,
  UserType
} from "../../../types/user";
import type { IPaginatedResponse, IMutationResponse } from "../../../types/base";

// Re-export the comprehensive User interface
export type { IUser } from "../../../types/user";

/**
 * Parameters for getting all users by company
 * Based on SPM User.getAllUsers static method
 */
export interface IGetAllUsersParams {
  companyId: string;
  limit?: number;
  skip?: number;
  search?: string;
  includeDeleted?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Parameters for finding user by email
 * Based on SPM User.findUserByEmail static method
 */
export interface IFindUserByEmailParams {
  companyId: string;
  email: string;
}

/**
 * Parameters for getting users by location
 * Based on SPM User.getByLocation static method
 */
export interface IGetUsersByLocationParams {
  locationId: string;
  limit?: number;
  skip?: number;
  search?: string;
  role?: UserRole;
  isActive?: boolean;
  includeDeleted?: boolean;
}

/**
 * Parameters for getting agency users by company
 * Based on SPM User.getAgencyUsersByCompanyId static method
 */
export interface IGetAgencyUsersParams {
  companyId: string;
  limit?: number;
  skip?: number;
  search?: string;
  includeDeleted?: boolean;
}

/**
 * Parameters for getting users by location and company
 * Based on SPM User.getByLocationCompany static method
 */
export interface IGetLocationCompanyUsersParams {
  locationId: string;
  companyId: string;
  role?: UserRole;
  limit?: number;
  skip?: number;
}

/**
 * Parameters for getting users by phone
 * Based on SPM User.getByPhone static method
 */
export interface IGetUsersByPhoneParams {
  locationId: string;
  phone: string;
  limit?: number;
}

/**
 * Parameters for user password operations
 */
export interface IUserPasswordParams {
  userId: string;
}

/**
 * Parameters for user smart list operations
 * Based on SPM User model smart list methods
 */
export interface IUserSmartListParams {
  userId: string;
  locationId?: string;
  removeAll?: boolean;
  removeGlobal?: boolean;
}

/**
 * Standard user list response
 */
export interface IUserListResponse {
  users: IUser[];
  total: number;
  count: number;
  skip: number;
  limit: number;
  hasMore: boolean;
}

/**
 * Single user response
 */
export interface IUserResponse {
  user: IUser;
  success: boolean;
  message?: string;
}

/**
 * Enhanced RTK Query API slice for users
 * Based on SPM User model static methods and functionality
 */
export const usersApi = createApi({
  reducerPath: "usersApi",
  baseQuery: lcAuthRequest,
  tagTypes: ["User"],
  endpoints: (builder) => ({
    /**
     * Get all users by company
     * Based on SPM User.getAllUsers static method
     */
    getAllUsers: builder.query<IUserListResponse, IGetAllUsersParams>({
      query: ({ companyId, limit = 50, skip = 0, search, includeDeleted = false, sortBy = 'firstName', sortOrder = 'asc' }) => ({
        url: "/users/",
        method: "GET",
        params: {
          companyId,
          limit,
          skip,
          ...(search && { search }),
          deleted: includeDeleted,
          sortBy,
          sortOrder,
        },
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.users.map(({ id }) => ({ type: "User" as const, id })),
              { type: "User", id: "ALL_USERS" },
            ]
          : [{ type: "User", id: "ALL_USERS" }],
    }),

    /**
     * Find user by email
     * Based on SPM User.findUserByEmail static method
     */
    findUserByEmail: builder.query<IUserResponse, IFindUserByEmailParams>({
      query: ({ companyId, email }) => ({
        url: "/users/search",
        method: "GET",
        params: {
          companyId,
          email,
          isActive: true,
          limit: 1,
        },
      }),
      providesTags: (result) =>
        result?.user ? [{ type: "User", id: result.user.id }] : [],
    }),

    /**
     * Get users by location
     * Based on SPM User.getByLocation static method
     */
    getUsersByLocation: builder.query<IUserListResponse, IGetUsersByLocationParams>({
      query: ({ locationId, limit = 50, skip = 0, search, role, isActive, includeDeleted = false }) => ({
        url: "/users/",
        method: "GET",
        params: {
          locationId,
          limit,
          skip,
          ...(search && { search }),
          ...(role && { role }),
          ...(isActive !== undefined && { isActive }),
          deleted: includeDeleted,
        },
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.users.map(({ id }) => ({ type: "User" as const, id })),
              { type: "User", id: "LOCATION_USERS" },
            ]
          : [{ type: "User", id: "LOCATION_USERS" }],
    }),

    /**
     * Get agency users by company
     * Based on SPM User.getAgencyUsersByCompanyId static method
     */
    getAgencyUsers: builder.query<IUserListResponse, IGetAgencyUsersParams>({
      query: ({ companyId, limit = 50, skip = 0, search, includeDeleted = false }) => ({
        url: "/users/",
        method: "GET",
        params: {
          companyId,
          type: 'agency',
          deleted: includeDeleted,
          limit,
          skip,
          ...(search && { search }),
        },
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.users.map(({ id }) => ({ type: "User" as const, id })),
              { type: "User", id: "AGENCY_USERS" },
            ]
          : [{ type: "User", id: "AGENCY_USERS" }],
    }),

    /**
     * Get users by location and company
     * Based on SPM User.getByLocationCompany static method
     */
    getLocationCompanyUsers: builder.query<IUserListResponse, IGetLocationCompanyUsersParams>({
      query: ({ locationId, companyId, role = 'admin', limit = 50, skip = 0 }) => ({
        url: "/users/",
        method: "GET",
        params: {
          locationId,
          companyId,
          role,
          limit,
          skip,
        },
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.users.map(({ id }) => ({ type: "User" as const, id })),
              { type: "User", id: "LOCATION_COMPANY_USERS" },
            ]
          : [{ type: "User", id: "LOCATION_COMPANY_USERS" }],
    }),

    /**
     * Get users by phone
     * Based on SPM User.getByPhone static method
     */
    getUsersByPhone: builder.query<IUserListResponse, IGetUsersByPhoneParams>({
      query: ({ locationId, phone, limit = 1 }) => ({
        url: "/users/",
        method: "GET",
        params: {
          locationId,
          phone,
          deleted: false,
          limit,
        },
      }),
      providesTags: (result) =>
        result
          ? result.users.map(({ id }) => ({ type: "User" as const, id }))
          : [],
    }),

    /**
     * Get user by ID
     */
    getUserById: builder.query<IUserResponse, { userId: string }>({
      query: ({ userId }) => ({
        url: `/users/${userId}`,
        method: "GET",
      }),
      providesTags: (result, error, { userId }) => [{ type: "User", id: userId }],
    }),

    /**
     * Create a new user
     */
    createUser: builder.mutation<IMutationResponse<IUser>, ICreateUserPayload>({
      query: (body) => ({
        url: "/users/",
        method: "POST",
        body,
      }),
      invalidatesTags: [
        { type: "User", id: "ALL_USERS" },
        { type: "User", id: "LOCATION_USERS" },
        { type: "User", id: "AGENCY_USERS" },
      ],
    }),

    /**
     * Update an existing user
     */
    updateUser: builder.mutation<IMutationResponse<IUser>, { userId: string } & IUpdateUserPayload>({
      query: ({ userId, ...body }) => ({
        url: `/users/${userId}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: (result, error, { userId }) => [
        { type: "User", id: userId },
        { type: "User", id: "ALL_USERS" },
        { type: "User", id: "LOCATION_USERS" },
        { type: "User", id: "AGENCY_USERS" },
      ],
    }),

    /**
     * Delete a user (soft delete)
     */
    deleteUser: builder.mutation<IMutationResponse, { userId: string }>({
      query: ({ userId }) => ({
        url: `/users/${userId}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, { userId }) => [
        { type: "User", id: userId },
        { type: "User", id: "ALL_USERS" },
        { type: "User", id: "LOCATION_USERS" },
        { type: "User", id: "AGENCY_USERS" },
      ],
    }),

    /**
     * Toggle user active status
     */
    toggleUserStatus: builder.mutation<IMutationResponse<IUser>, { userId: string; isActive: boolean }>({
      query: ({ userId, isActive }) => ({
        url: `/users/${userId}/status`,
        method: "PATCH",
        body: { isActive },
      }),
      invalidatesTags: (result, error, { userId }) => [
        { type: "User", id: userId },
        { type: "User", id: "ALL_USERS" },
        { type: "User", id: "LOCATION_USERS" },
      ],
    }),

    /**
     * Handle user password change
     * Based on SPM User.passwordChanged method
     */
    handlePasswordChanged: builder.mutation<IMutationResponse, IUserPasswordParams>({
      query: ({ userId }) => ({
        url: `/users/${userId}/password-changed`,
        method: "POST",
      }),
      invalidatesTags: (result, error, { userId }) => [
        { type: "User", id: userId },
      ],
    }),

    /**
     * Sync global lists for all locations
     * Based on SPM User.syncGlobalListsForAllLocation method
     */
    syncGlobalListsForAllLocations: builder.mutation<IMutationResponse, { userId: string }>({
      query: ({ userId }) => ({
        url: `/users/${userId}/sync-global-lists`,
        method: "POST",
      }),
    }),

    /**
     * Sync global lists for specific location
     * Based on SPM User.syncGlobalListsForLocation method
     */
    syncGlobalListsForLocation: builder.mutation<IMutationResponse, IUserSmartListParams>({
      query: ({ userId, locationId }) => ({
        url: `/users/${userId}/sync-global-lists`,
        method: "POST",
        body: { locationId },
      }),
    }),

    /**
     * Remove smart lists for location
     * Based on SPM User smart list removal methods
     */
    removeSmartListsForLocation: builder.mutation<IMutationResponse, IUserSmartListParams>({
      query: ({ userId, locationId, removeAll, removeGlobal }) => ({
        url: `/users/${userId}/remove-smart-lists`,
        method: "POST",
        body: {
          locationId,
          removeAll: removeAll || false,
          removeGlobal: removeGlobal || false
        },
      }),
    }),
  }),
});

/**
 * Export hooks for use in components
 */
export const {
  // Query hooks
  useGetAllUsersQuery,
  useFindUserByEmailQuery,
  useGetUsersByLocationQuery,
  useGetAgencyUsersQuery,
  useGetLocationCompanyUsersQuery,
  useGetUsersByPhoneQuery,
  useGetUserByIdQuery,

  // Mutation hooks
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useToggleUserStatusMutation,
  useHandlePasswordChangedMutation,
  useSyncGlobalListsForAllLocationsMutation,
  useSyncGlobalListsForLocationMutation,
  useRemoveSmartListsForLocationMutation,
} = usersApi;

export default usersApi;
