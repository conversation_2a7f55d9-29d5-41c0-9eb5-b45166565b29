/**
 * API Slices Index
 * 
 * This file exports all RTK Query API slices and their hooks for use throughout the application.
 * Each API slice is organized by functionality and provides type-safe endpoints for interacting
 * with the LeadConnector backend.
 */

// Notifications API
export {
  default as notificationsApi,
  useGetNotificationsQuery,
  useGetNotificationReportingQuery,
  useMarkNotificationAsReadMutation,
  useMarkAllNotificationsAsReadMutation,
} from "./notificationsApi";

export type {
  INotification,
  IGetNotificationsParams,
  IGetNotificationsResponse,
  INotificationReportingParams,
  INotificationReportingResponse,
} from "./notificationsApi";

// Custom Fields API
export {
  default as customFieldsApi,
  useGetCustomFieldFoldersQuery,
  useGetCustomFieldsQuery,
  useCreateCustomFieldMutation,
  useUpdateCustomFieldMutation,
  useDeleteCustomFieldMutation,
} from "./customFieldsApi";

export type {
  I<PERSON>ustom<PERSON>ield,
  ICustomFieldFolder,
  ICustomFieldsSearchParams,
  ICustomFieldFoldersResponse,
  ICustomFieldsResponse,
  ICreateCustomFieldRequest,
  IUpdateCustomFieldRequest,
} from "./customFieldsApi";

// Users API - Enhanced
export {
  default as usersApi,
  useGetAllUsersQuery,
  useFindUserByEmailQuery,
  useGetUsersByLocationQuery,
  useGetAgencyUsersQuery,
  useGetLocationCompanyUsersQuery,
  useGetUsersByPhoneQuery,
  useGetUserByIdQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useToggleUserStatusMutation,
  useHandlePasswordChangedMutation,
  useSyncGlobalListsForAllLocationsMutation,
  useSyncGlobalListsForLocationMutation,
  useRemoveSmartListsForLocationMutation,
} from "./usersApi";

export type {
  IUser,
} from "./usersApi";

// Locations API
export {
  default as locationsApi,
  useSearchLocationsQuery,
  useGetLocationByIdQuery,
  useCreateLocationMutation,
  useUpdateLocationMutation,
  useDeleteLocationMutation,
  useRefreshLocationTokenMutation,
  useToggleLocationStatusMutation,
} from "./locationsApi";

export type {
  ILocation,
  ISearchLocationsParams,
  ISearchLocationsResponse,
  IGetLocationByIdParams,
  IGetLocationByIdResponse,
  ICreateLocationRequest,
  IUpdateLocationRequest,
  ILocationRefreshTokenRequest,
  ILocationRefreshTokenResponse,
} from "./locationsApi";

// SubAccount API
export {
  subAccountApi,
  useGetSubAccountsQuery,
  useGetSubAccountByIdQuery,
  useCreateSubAccountMutation,
  useUpdateSubAccountMutation,
  useDeleteSubAccountMutation,
  useGetActiveSubAccountsQuery,
  useSearchSubAccountsQuery,
  useGetSubAccountsByStatusQuery,
  useUpdateSubAccountStatusMutation,
  useUpdateSubAccountSettingsMutation,
  useGetSubAccountBySnapshotIdQuery,
  useBulkUpdateSubAccountsMutation,
  useLazyGetSubAccountsQuery,
  useLazySearchSubAccountsQuery,
  useLazyGetSubAccountsByStatusQuery,
} from "./subAccountApi";

export type {
  ISubAccount,
  ICreateSubAccountPayload,
  IUpdateSubAccountPayload,
  ISubAccountQueryParams,
  ISubAccountSearchResponse,
  SubAccountStatus,
  SaasProduct,
  ISubAccountSettings,
  IProductStatus,
} from "../../../types/subAccount";

// Import the API slices
import notificationsApi from "./notificationsApi";
import customFieldsApi from "./customFieldsApi";
import usersApi from "./usersApi";
import locationsApi from "./locationsApi";
import { subAccountApi } from "./subAccountApi";

/**
 * Combined API slices for store configuration
 */
export const apiSlices = {
  notificationsApi,
  customFieldsApi,
  usersApi,
  locationsApi,
  subAccountApi,
} as const;

/**
 * API reducer paths for store configuration
 */
export const apiReducerPaths = {
  notifications: "notificationsApi",
  customFields: "customFieldsApi",
  users: "usersApi",
  locations: "locationsApi",
  subAccounts: "subAccountApi",
} as const;
