import type { Action, ThunkAction } from "@reduxjs/toolkit";
import { combineSlices, configureStore } from "@reduxjs/toolkit";
import { useDispatch, useSelector, useStore } from "react-redux";
import authSlice from "./features/auth/authSlice";
import { userSlice } from "./features/user/userSlice";
import { subAccountSlice } from "./features/subAccount/subAccountSlice";
import { authApiSlice } from "./features/auth/authApiSlice";
import {
  notificationsApi,
  customFieldsApi,
  usersApi,
  locationsApi,
  subAccountApi
} from "./features/api";
import { setupListeners } from "@reduxjs/toolkit/query";

// `combineSlices` automatically combines the reducers using
// their `reducerPath`s, therefore we no longer need to call `combineReducers`.
const rootReducer = combineSlices(
  authSlice,
  userSlice,
  subAccountSlice,
  authApiSlice,
  notificationsApi,
  customFieldsApi,
  usersApi,
  locationsApi,
  subAccountApi
);
// Infer the `RootState` type from the root reducer
export type RootState = ReturnType<typeof rootReducer>;

// `makeStore` encapsulates the store configuration to allow
// creating unique store instances, which is particularly important for
// server-side rendering (SSR) scenarios. In SSR, separate store instances
// are needed for each request to prevent cross-request state pollution.
export const makeStore = () => {
  const store = configureStore({
    reducer: rootReducer,
    // Adding the api middleware enables caching, invalidation, polling,
    // and other useful features of `rtk-query`.
    middleware: (getDefaultMiddleware) => {
      return getDefaultMiddleware({
        serializableCheck: {
          // Ignore these action types for serialization checks
          ignoredActions: [
            // RTK Query actions that may contain non-serializable values
            'persist/PERSIST',
            'persist/REHYDRATE',
          ],
          // All auth actions now use serializable data only
        },
      }).concat(
        authApiSlice.middleware,
        notificationsApi.middleware,
        customFieldsApi.middleware,
        usersApi.middleware,
        locationsApi.middleware,
        subAccountApi.middleware
      );
    },
  });

  // Setup RTK Query listeners for automatic refetching
  setupListeners(store.dispatch);

  return store;
};

// Infer the return type of `makeStore`
export type AppStore = ReturnType<typeof makeStore>;
// Infer the `AppDispatch` type from the store itself
export type AppDispatch = AppStore["dispatch"];
export type AppThunk<ThunkReturnType = void> = ThunkAction<
  ThunkReturnType,
  RootState,
  unknown,
  Action
>;

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = useDispatch.withTypes<AppDispatch>();
export const useAppSelector = useSelector.withTypes<RootState>();
export const useAppStore = useStore.withTypes<AppStore>();
