import { z } from "zod";
import { fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import type { RootState } from "../store";

export const getLCRequestCommonHeaders = (): Record<string, string> => {
  return {
    "Content-Type": "application/json",
    Accept: "application/json, text/plain, */*",
    "accept-language": "en-US,en;q=0.9",
    source: "WEB_USER",
    channel: "APP",
    version: "2021-07-28",
  };
};

export const getFirstZodError = <T>(
  result: z.SafeParseReturnType<T, T>
): string => {
  if (!result.success && result.error?.errors?.[0]?.message) {
    return result.error.errors[0].message;
  }
  return "An error occurred, please try again or contact support if the issue persists.";
};

/**
 * Device information interface for authentication payload
 */
export interface DeviceInfo {
  deviceId: string;
  deviceName?: string;
  deviceType?: string;
  domain?: string;
}

/**
 * Generates device information for authentication requests
 * @returns Device information object with deviceId and browser details
 */
export const getDevice = (): DeviceInfo => {
  // Generate a unique device ID based on browser fingerprint
  const generateDeviceId = (): string => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Device fingerprint', 2, 2);
    }

    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL(),
      navigator.hardwareConcurrency || 0,
      (navigator as any).deviceMemory || 0,
    ].join('|');

    // Create a simple hash of the fingerprint
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    return Math.abs(hash).toString(36);
  };

  // Detect device type based on user agent
  const getDeviceType = (): string => {
    const userAgent = navigator.userAgent.toLowerCase();

    if (/mobile|android|iphone|ipad|phone/i.test(userAgent)) {
      return 'mobile';
    } else if (/tablet|ipad/i.test(userAgent)) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  };

  // Get device/browser name
  const getDeviceName = (): string => {
    const userAgent = navigator.userAgent;

    if (userAgent.includes('Chrome')) {
      return 'Chrome';
    } else if (userAgent.includes('Firefox')) {
      return 'Firefox';
    } else if (userAgent.includes('Safari')) {
      return 'Safari';
    } else if (userAgent.includes('Edge')) {
      return 'Edge';
    } else {
      return 'Unknown Browser';
    }
  };

  // Get current domain
  const getDomain = (): string => {
    return window.location.hostname;
  };

  return {
    deviceId: generateDeviceId(),
    deviceName: getDeviceName(),
    deviceType: getDeviceType(),
    domain: getDomain(),
  };
};

/**
 * Authenticated base query for RTK Query
 * Automatically adds authentication headers and common LC headers
 */

export const lcAuthRequest = fetchBaseQuery({
  baseUrl: "https://backend.leadconnectorhq.com",
  prepareHeaders: (headers, { getState }) => {
    // Add common LC headers
    const commonHeaders = getLCRequestCommonHeaders();
    Object.entries(commonHeaders).forEach(([key, value]) => {
      headers.set(key, value);
    });

    // Add authentication headers if available
    const state = getState() as RootState;
    const authData = state.auth.authData;

    if (authData?.authToken) {
      headers.set("authorization", `Bearer ${authData.authToken}`);
    }

    if (authData?.firebaseToken) {
      headers.set("token-id", authData.firebaseToken);
    }

    return headers;
  },
});

// Export model utilities
export {
  DateUtils,
  ValidationUtils,
  ChangeTracker,
  EntityUtils
} from './modelUtils';

// Export user utilities
export {
  UserComputedUtils,
  UserPermissionUtils,
  UserLocationUtils,
  UserValidationUtils
} from './userUtils';

// Export sub-account utilities
export {
  SubAccountComputedProperties,
  SubAccountValidationUtils,
  SubAccountBusinessUtils
} from './subAccountUtils';
